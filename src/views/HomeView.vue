<template>
  <div class="home-container">
    <div class="welcome-section">
      <h1>欢迎使用智能统一归因平台</h1>
      <p class="subtitle">选择您想要使用的功能模块</p>
    </div>

    <div class="modules-grid">
      <div class="module-card" @click="navigateTo('/inductive')">
        <div class="module-icon">🔍</div>
        <h2>引导式归因</h2>
        <p>使用引导式归因，探索数据趋势和模式</p>
        <button class="enter-button">进入分析</button>
      </div>

      <div class="module-card" @click="navigateTo('/chat')">
        <div class="module-icon">💬</div>
        <h2>智能对话</h2>
        <p>通过自然语言交互，获取数据洞察和建议</p>
        <button class="enter-button">开始对话</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStates } from '@/store/states'

const router = useRouter()
const statesStore = useStates()

const navigateTo = (path) => {
  router.push(path)
}

onMounted(() => {
  // TODO: demo标题
  statesStore.$patch({ pageName: 'home', title: '智能统一归因平台' })
})
</script>

<style scoped>
.home-container {
  /* min-height: 80vh; */
  padding: 36px 18px; /* 40px * 0.9, 20px * 0.9 */
  background: #f7faff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.welcome-section {
  text-align: center;
  margin-bottom: 54px; /* 60px * 0.9 */
}

.welcome-section h1 {
  font-size: 2.25rem; /* 2.5rem * 0.9 */
  color: #2c3e50;
  margin-bottom: 14.4px; /* 16px * 0.9 */
}

.subtitle {
  font-size: 1.08rem; /* 1.2rem * 0.9 */
  color: #666;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr)); /* 300px * 0.9 */
  gap: 27px; /* 30px * 0.9 */
  max-width: 1080px; /* 1200px * 0.9 */
  width: 100%;
  padding: 0 18px; /* 20px * 0.9 */
}

.module-card {
  background: white;
  border-radius: 10.8px; /* 12px * 0.9 */
  padding: 27px; /* 30px * 0.9 */
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1.8px 7.2px rgba(0, 0, 0, 0.04); /* 2px * 0.9, 8px * 0.9 */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 14.4px; /* 16px * 0.9 */
}

.module-card:hover {
  transform: translateY(-4.5px); /* -5px * 0.9 */
  box-shadow: 0 3.6px 10.8px rgba(0, 0, 0, 0.08); /* 4px * 0.9, 12px * 0.9 */
}

.module-icon {
  font-size: 2.7rem; /* 3rem * 0.9 */
  margin-bottom: 9px; /* 10px * 0.9 */
}

.module-card h2 {
  color: #2c3e50;
  font-size: 1.35rem; /* 1.5rem * 0.9 */
  margin: 0;
}

.module-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.enter-button {
  margin-top: auto;
  padding: 10.8px 21.6px; /* 12px * 0.9, 24px * 0.9 */
  background: #42b983;
  color: white;
  border: none;
  border-radius: 5.4px; /* 6px * 0.9 */
  font-size: 0.9rem; /* 1rem * 0.9 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.enter-button:hover {
  background: #3aa876;
  transform: translateY(-0.9px); /* -1px * 0.9 */
  box-shadow: 0 1.8px 4.5px rgba(66, 185, 131, 0.2); /* 2px * 0.9, 5px * 0.9 */
}

@media (max-width: 691px) { /* 768px * 0.9 */
  .modules-grid {
    grid-template-columns: 1fr;
    max-width: 360px; /* 400px * 0.9 */
  }

  .welcome-section h1 {
    font-size: 1.8rem; /* 2rem * 0.9 */
  }

  .module-card {
    padding: 18px; /* 20px * 0.9 */
  }
}
</style>
