<template>
  <div>
    <div v-if="props.readonly">
      <div ref="chartRef" style="width: 100%; height: 340px"></div>
    </div>
    <template v-else>
      <!-- 可视化配置弹窗 -->
      <div v-if="showVizModal" class="chartviz-modal-mask">
        <div class="chartviz-modal-box">
          <div class="chartviz-modal-header">
            <h3 class="chartviz-modal-title">可视化配置</h3>
            <button class="chartviz-modal-close" @click="handleCloseConfig">×</button>
          </div>
          <div class="chartviz-modal-content">
            <div class="chartviz-form-row">
              <label class="chartviz-label">图表类型</label>
              <select v-model="localConfig.chartType" class="chartviz-select">
                <option v-for="t in chartTypes" :key="t.value" :value="t.value">
                  {{ t.label }}
                </option>
              </select>
            </div>
            <div class="chartviz-form-row" v-if="localConfig.chartType === 'scatter'">
              <label class="chartviz-label">X轴字段</label>
              <select v-model="localConfig.xAxis" class="chartviz-select">
                <option v-for="col in numericColumns" :key="col.field" :value="col.field">
                  {{ col.title }}
                </option>
              </select>
            </div>
            <div class="chartviz-form-row" v-if="localConfig.chartType === 'scatter'">
              <label class="chartviz-label">Y轴字段</label>
              <select v-model="localConfig.yAxis" class="chartviz-select">
                <option v-for="col in numericColumns" :key="col.field" :value="col.field">
                  {{ col.title }}
                </option>
              </select>
            </div>
            <div
              class="chartviz-form-row"
              v-if="localConfig.chartType !== 'pie' && localConfig.chartType !== 'scatter'"
            >
              <label class="chartviz-label">Y轴字段</label>
              <select
                v-model="localConfig.yAxis"
                class="chartviz-select"
                multiple
                style="min-height: 40px"
              >
                <option v-for="col in numericColumns" :key="col.field" :value="col.field">
                  {{ col.title }}
                </option>
              </select>
              <span
                v-if="localConfig.chartType === 'area'"
                style="color: #888; font-size: 12px; margin-left: 8px"
                >面积图本质为带填充的折线图</span
              >
            </div>
            <div class="chartviz-form-row" v-if="localConfig.chartType === 'pie'">
              <label class="chartviz-label">数值字段</label>
              <select v-model="localConfig.yAxis" class="chartviz-select">
                <option v-for="col in numericColumns" :key="col.field" :value="col.field">
                  {{ col.title }}
                </option>
              </select>
            </div>
            <div class="chartviz-form-row">
              <label class="chartviz-label">图表标题</label>
              <input v-model="localConfig.title" class="chartviz-input" placeholder="可选" />
            </div>
            <div class="chartviz-form-row">
              <label class="chartviz-label">显示图例</label>
              <input
                type="checkbox"
                v-model="localConfig.legend"
                style="width: 18px; height: 18px; vertical-align: middle"
              />
            </div>
            <div class="chartviz-form-row">
              <label class="chartviz-label">主色调</label>
              <div style="display: flex; gap: 8px; align-items: center">
                <div
                  v-for="c in palette"
                  :key="c"
                  :style="{
                    width: '28px',
                    height: '28px',
                    borderRadius: '6px',
                    background: c,
                    border: localConfig.color === c ? '2px solid #1976d2' : '1.5px solid #b3d1f7',
                    cursor: 'pointer',
                  }"
                  @click="localConfig.color = c"
                ></div>
                <input
                  v-model="localConfig.color"
                  class="chartviz-input"
                  type="color"
                  style="width: 40px; height: 32px; padding: 0; border: none; background: none"
                />
              </div>
            </div>
            <div class="chartviz-form-row" v-if="localConfig.chartType === 'scatter'">
              <label class="chartviz-label">趋势线</label>
              <input
                type="checkbox"
                v-model="localConfig.trendLine"
                style="width: 18px; height: 18px; vertical-align: middle"
              />
              <span style="color: #888; font-size: 12px; margin-left: 8px"
                >自动拟合
                <select
                  v-model="localConfig.trendLineType"
                  style="margin-left: 4px; font-size: 12px"
                >
                  <option value="linear">线性</option>
                  <option value="exponential">指数</option>
                </select>
                趋势
              </span>
            </div>
          </div>
          <div class="chartviz-modal-footer">
            <button class="chartviz-btn-secondary" @click="handleCloseConfig">取消</button>
            <button class="chartviz-btn-primary" @click="handleConfirmConfig">生成图表</button>
          </div>
        </div>
      </div>
      <!-- 可视化图表弹窗 -->
      <div v-if="showChart" class="chartviz-modal-mask">
        <div
          class="chartviz-modal-box"
          style="
            min-width: 700px;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            align-items: stretch;
          "
        >
          <div class="chartviz-modal-header">
            <h3 class="chartviz-modal-title">数据可视化</h3>
            <div style="flex: 1"></div>
            <button
              class="chartviz-modal-edit"
              @click="handleEditConfig"
              style="
                margin-right: 12px;
                color: #1976d2;
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
              "
            >
              编辑
            </button>
            <button
              class="chartviz-modal-download"
              @click="handleDownloadChart"
              style="
                margin-right: 12px;
                color: #1976d2;
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
              "
            >
              下载
            </button>
            <button
              class="chartviz-modal-addtodashboard"
              @click="handleAddToDashboard"
              style="
                margin-right: 12px;
                color: #42b983;
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
              "
            >
              加入看板
            </button>
            <button class="chartviz-modal-close" @click="handleCloseChart">×</button>
          </div>
          <div
            style="
              flex: 1;
              min-height: 400px;
              display: flex;
              align-items: center;
              justify-content: center;
            "
          >
            <div ref="chartRef" style="width: 100%; height: 400px"></div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick, onUnmounted, onMounted } from 'vue'
import { use } from 'echarts/core'
import { BarChart, LineChart, PieChart, ScatterChart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import * as echarts from 'echarts'

// 注册ECharts组件
use([
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  CanvasRenderer,
])

// Props
const props = defineProps({
  show: Boolean, // 控制可视化入口显示
  data: { type: Array, required: true },
  columns: { type: Array, required: true },
  initialConfig: { type: Object, default: () => ({}) },
  readonly: { type: Boolean, default: false },
})

// Emits
const emit = defineEmits(['close', 'update:show', 'configChange', 'add-to-dashboard'])

// 内部状态
const showVizModal = ref(props.show)
const showChart = ref(false)
const chartRef = ref(null)
const chartInstance = ref(null)

// 预设科技蓝色板
const palette = [
  '#1976d2', // 科技蓝主色
  '#26a6f7', // 浅蓝
  '#00bcd4', // 青色
  '#42a5f5', // 亮蓝
  '#1565c0', // 深蓝
  '#90caf9', // 浅灰蓝
  '#1de9b6', // 青绿
  '#5c6bc0', // 蓝紫
  '#00acc1', // 蓝绿
  '#2196f3', // 标准蓝
]

// 配置表单本地副本
const defaultConfig = () => ({
  xAxis: '',
  yAxis: [],
  chartType: 'bar',
  title: '',
  legend: true,
  color:
    props.initialConfig.color && props.initialConfig.color !== ''
      ? props.initialConfig.color
      : palette[0],
  trendLine: false,
  trendLineType: 'linear',
  ...props.initialConfig,
  color:
    props.initialConfig.color && props.initialConfig.color !== ''
      ? props.initialConfig.color
      : palette[0],
})
const localConfig = ref(defaultConfig())

// 每次弹窗打开时重置localConfig
watch(showVizModal, (val) => {
  if (val) {
    localConfig.value = defaultConfig()
  }
})

// 图表类型
const chartTypes = [
  { value: 'bar', label: '柱状图' },
  { value: 'line', label: '折线图' },
  { value: 'area', label: '面积图' },
  { value: 'pie', label: '饼图' },
  { value: 'scatter', label: '散点图' },
]

// 数值型字段
const numericColumns = computed(() =>
  props.columns.filter(
    (c) =>
      c &&
      (c.type === '数值型' ||
        typeof c.type === 'undefined' ||
        typeof props.data[0]?.[c.field] === 'number'),
  ),
)

// 关闭配置弹窗
function handleCloseConfig() {
  showVizModal.value = false
  emit('close')
  emit('update:show', false)
}
// 关闭图表弹窗
function handleCloseChart() {
  showChart.value = false
  emit('close')
  emit('update:show', false)
}
// 编辑按钮
function handleEditConfig() {
  showChart.value = false
  showVizModal.value = true
}
// 确认生成图表
function handleConfirmConfig() {
  showVizModal.value = false
  showChart.value = true
  nextTick(() => renderChart())
  emit('configChange', { ...localConfig.value })
}

// 下载图表为图片
function handleDownloadChart() {
  if (chartInstance.value) {
    const url = chartInstance.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
    })
    const link = document.createElement('a')
    link.href = url
    link.download = (localConfig.value.title || 'chart') + '.png'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 加入看板
function handleAddToDashboard() {
  emit('add-to-dashboard', {
    config: { ...localConfig.value },
    data: props.data,
    columns: props.columns,
  })
}

// 生成series
function buildSeries(type, data, yAxis, xAxis, palette, localConfig, extra) {
  if (type === 'scatter') {
    const xField = xAxis
    const yField = yAxis
    const scatterData = data.map((row) => [row[xField], row[yField]])
    const base = [
      {
        name: `${xField} vs ${yField}`,
        type: 'scatter',
        data: scatterData,
        symbolSize: 10,
        emphasis: { focus: 'series' },
        itemStyle: { color: palette[0] },
      },
    ]
    // 趋势线
    if (localConfig.trendLine && extra && extra.fitLine) {
      base.push({
        name: '趋势线',
        type: 'line',
        data: extra.fitLine,
        showSymbol: false,
        lineStyle: { type: 'dashed', color: palette[1], width: 2 },
        emphasis: { focus: 'series' },
        markPoint: {
          data: [{ coord: extra.fitLine[0], value: extra.formula }],
          symbol: 'none',
          label: { show: true, color: palette[1], fontWeight: 'bold', fontSize: 12 },
        },
        tooltip: { show: true, formatter: `趋势线: ${extra.formula}` },
      })
    }
    return base
  }
  if (type === 'area') {
    return (Array.isArray(yAxis) ? yAxis : [yAxis]).map((field, idx) => ({
      name: getColTitle(field),
      type: 'line',
      data: data.map((row) => row[field]),
      areaStyle: {},
      smooth: true,
      itemStyle: { color: palette[idx % palette.length] },
      lineStyle: { color: palette[idx % palette.length] },
    }))
  }
  // line/bar/pie
  return (Array.isArray(yAxis) ? yAxis : [yAxis]).map((field, idx) => ({
    name: getColTitle(field),
    type: type === 'pie' ? 'pie' : type,
    data:
      type === 'pie'
        ? data.map((row) => ({ value: row[field], name: row[xAxis] }))
        : data.map((row) => row[field]),
    ...(type === 'pie' ? { radius: '60%', center: ['50%', '55%'] } : {}),
    itemStyle: type !== 'pie' ? { color: palette[idx % palette.length] } : undefined,
    lineStyle: type === 'line' ? { color: palette[idx % palette.length] } : undefined,
  }))
}

// 生成option
function buildOption(type, series, xAxis, yAxis, localConfig, palette, extra) {
  let option = {
    title: localConfig.title ? { text: localConfig.title, left: 'center', top: 0 } : undefined,
    tooltip:
      type === 'scatter'
        ? {
            trigger: 'item',
            formatter: function (params) {
              if (params.seriesType === 'scatter') {
                const xLabel = typeof xAxis === 'string' ? getColTitle(xAxis) || xAxis : xAxis
                const yLabel = typeof yAxis === 'string' ? getColTitle(yAxis) || yAxis : yAxis
                return `${xLabel}: ${params.value[0]}<br/>${yLabel}: ${params.value[1]}`
              } else if (params.seriesType === 'line' && params.seriesName === '趋势线') {
                return params.seriesName + (extra && extra.formula ? `<br/>${extra.formula}` : '')
              }
              return params.seriesName
            },
          }
        : { trigger: type === 'pie' ? 'item' : 'axis' },
    legend: localConfig.legend ? { top: 30 } : undefined,
    color: palette,
    series,
  }
  if (type === 'scatter') {
    option.xAxis = { type: 'value', name: getColTitle(xAxis), min: extra.minX, max: extra.maxX }
    option.yAxis = { type: 'value', name: getColTitle(yAxis), min: extra.minY, max: extra.maxY }
  } else if (type === 'pie') {
    // no axis
  } else {
    option.xAxis = { type: 'category', data: extra.xData }
    option.yAxis = { type: 'value' }
  }
  return option
}

function renderChart() {
  if (!chartRef.value) return
  if (chartInstance.value) chartInstance.value.dispose()
  chartInstance.value = echarts.init(chartRef.value)
  const type = localConfig.value.chartType
  const data = props.data
  const paletteUsed = [
    localConfig.value.color || palette[0],
    ...palette.filter((c) => c !== localConfig.value.color),
  ]
  let extra = {}
  let series = []
  if (type === 'scatter') {
    const xField = localConfig.value.xAxis
    const yField = localConfig.value.yAxis
    if (!xField || !yField) {
      chartInstance.value.setOption({ title: { text: '请选择X和Y轴字段', left: 'center' } })
      return
    }
    const scatterData = data.map((row) => [row[xField], row[yField]])
    // 自动调整x/y轴区间
    const xVals = scatterData.map(([x]) => Number(x)).filter((v) => !isNaN(v))
    const yVals = scatterData.map(([_, y]) => Number(y)).filter((v) => !isNaN(v))
    let minX = Math.min(...xVals),
      maxX = Math.max(...xVals)
    let minY = Math.min(...yVals),
      maxY = Math.max(...yVals)
    if (minX === maxX) {
      minX -= 1
      maxX += 1
    }
    if (minY === maxY) {
      minY -= 1
      maxY += 1
    }
    const xPad = (maxX - minX) * 0.05
    const yPad = (maxY - minY) * 0.05
    minX -= xPad
    maxX += xPad
    minY -= yPad
    maxY += yPad
    // 趋势线
    if (localConfig.value.trendLine) {
      let fitLine = []
      let formula = ''
      if (localConfig.value.trendLineType === 'linear') {
        const n = scatterData.length
        let sumX = 0,
          sumY = 0,
          sumXY = 0,
          sumXX = 0
        scatterData.forEach(([x, y]) => {
          sumX += Number(x)
          sumY += Number(y)
          sumXY += Number(x) * Number(y)
          sumXX += Number(x) * Number(x)
        })
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
        const intercept = (sumY - slope * sumX) / n
        fitLine = [
          [minX, slope * minX + intercept],
          [maxX, slope * maxX + intercept],
        ]
        formula = `y=${slope.toFixed(2)}x+${intercept.toFixed(2)}`
      } else if (localConfig.value.trendLineType === 'exponential') {
        const n = scatterData.length
        let sumX = 0,
          sumLogY = 0,
          sumXLogY = 0,
          sumXX = 0
        scatterData.forEach(([x, y]) => {
          const logY = Math.log(Number(y))
          sumX += Number(x)
          sumLogY += logY
          sumXLogY += Number(x) * logY
          sumXX += Number(x) * Number(x)
        })
        const b = (n * sumXLogY - sumX * sumLogY) / (n * sumXX - sumX * sumX)
        const logA = (sumLogY - b * sumX) / n
        const a = Math.exp(logA)
        fitLine = []
        for (let i = 0; i <= 50; i++) {
          const x = minX + (maxX - minX) * (i / 50)
          fitLine.push([x, a * Math.exp(b * x)])
        }
        formula = `y=${a.toFixed(2)}e^(${b.toFixed(2)}x)`
      }
      extra.fitLine = fitLine
      extra.formula = formula
    }
    extra.minX = minX
    extra.maxX = maxX
    extra.minY = minY
    extra.maxY = maxY
    series = buildSeries(type, data, yField, xField, paletteUsed, localConfig.value, extra)
  } else {
    const xField = localConfig.value.xAxis
    const yFields = localConfig.value.yAxis
    const xData = data.map((row) => row[xField])
    extra.xData = xData
    series = buildSeries(type, data, yFields, xField, paletteUsed, localConfig.value, extra)
  }
  const option = buildOption(
    type,
    series,
    localConfig.value.xAxis,
    localConfig.value.yAxis,
    localConfig.value,
    paletteUsed,
    extra,
  )
  chartInstance.value.setOption(option)
}

// 工具函数
function getColTitle(field) {
  const col = props.columns.find((c) => c.field === field)
  return col?.title || field
}

// 监听props.show
watch(
  () => props.show,
  (val) => {
    if (val) showVizModal.value = true
  },
)

// Render chart in readonly mode
onMounted(() => {
  if (props.readonly) {
    nextTick(() => renderChart())
  }
})
watch(
  [() => props.data, () => props.columns, () => props.initialConfig, () => props.readonly],
  () => {
    if (props.readonly) {
      nextTick(() => renderChart())
    }
  },
)

// 组件卸载时销毁echarts实例
onUnmounted(() => {
  if (chartInstance.value) chartInstance.value.dispose()
})
</script>

<style scoped>
.chartviz-modal-mask {
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}
.chartviz-modal-box {
  background: #fff;
  border-radius: 16.2px; /* 18px * 0.9 */
  box-shadow:
    0 7.2px 28.8px rgba(25, 118, 210, 0.13), /* 8px * 0.9, 32px * 0.9 */
    0 1.8px 7.2px rgba(0, 0, 0, 0.06); /* 2px * 0.9, 8px * 0.9 */
  min-width: 630px; /* 700px * 0.9 */
  max-width: 96vw;
  min-height: 450px; /* 500px * 0.9 */
  overflow: hidden;
  padding: 0 0 28.8px 0; /* 32px * 0.9 */
  display: flex;
  flex-direction: column;
}
.chartviz-modal-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 25.2px 36px 14.4px 36px; /* 28px * 0.9, 40px * 0.9, 16px * 0.9 */
  border-bottom: 1.35px solid #e3f2fd; /* 1.5px * 0.9 */
}
.chartviz-modal-title {
  font-size: 1.35rem; /* 1.5rem * 0.9 */
  color: #1976d2;
  font-weight: bold;
  margin: 0;
  letter-spacing: 0.9px; /* 1px * 0.9 */
}
.chartviz-modal-close,
.chartviz-modal-edit {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 14.4px; /* 16px * 0.9 */
  cursor: pointer;
}
.chartviz-modal-close {
  margin-left: 10.8px; /* 12px * 0.9 */
}
.chartviz-modal-edit {
  margin-right: 10.8px; /* 12px * 0.9 */
}
.chartviz-modal-content {
  padding: 28.8px 36px 0 36px; /* 32px * 0.9, 40px * 0.9 */
}
.chartviz-form-row {
  display: flex;
  align-items: center;
  margin-bottom: 16.2px; /* 18px * 0.9 */
}
.chartviz-label {
  width: 90px; /* 100px * 0.9 */
  font-weight: 500;
  color: #1976d2;
  font-size: 0.909rem; /* 1.01rem * 0.9 */
}
.chartviz-select,
.chartviz-input {
  flex: 1;
  padding: 6.3px 16.2px 6.3px 9px; /* 7px * 0.9, 18px * 0.9, 10px * 0.9 */
  border-radius: 5.4px; /* 6px * 0.9 */
  border: 1.35px solid #b3d1f7; /* 1.5px * 0.9 */
  background: #fff;
  font-size: 0.909rem; /* 1.01rem * 0.9 */
  color: #1976d2;
  outline: none;
  transition: border 0.2s;
  box-shadow: 0 0.9px 1.8px rgba(25, 118, 210, 0.04); /* 1px * 0.9, 2px * 0.9 */
}
.chartviz-select:focus,
.chartviz-input:focus {
  border: 1.35px solid #1976d2; /* 1.5px * 0.9 */
}
.chartviz-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 14.4px; /* 16px * 0.9 */
  padding: 16.2px 36px 0 36px; /* 18px * 0.9, 40px * 0.9 */
}
.chartviz-btn-primary {
  padding: 7.2px 21.6px; /* 8px * 0.9, 24px * 0.9 */
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 5.4px; /* 6px * 0.9 */
  font-size: 13.5px; /* 15px * 0.9 */
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.chartviz-btn-primary:hover {
  background: #1565c0;
}
.chartviz-btn-secondary {
  padding: 7.2px 21.6px; /* 8px * 0.9, 24px * 0.9 */
  background: #f5f5f5;
  color: #1976d2;
  border: 1.35px solid #b3d1f7; /* 1.5px * 0.9 */
  border-radius: 5.4px; /* 6px * 0.9 */
  font-size: 13.5px; /* 15px * 0.9 */
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.chartviz-btn-secondary:hover {
  background: #e3f2fd;
}
</style>
