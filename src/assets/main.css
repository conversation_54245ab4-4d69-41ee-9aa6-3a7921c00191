@import './base.css';

#app {
  max-width: 1152px; /* 1280px * 0.9 */
  margin: 0 auto;
  padding: var(--base-padding);
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 2.7px; /* 3px * 0.9 */
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 921px) { /* 1024px * 0.9 */
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 var(--base-padding);
  }
}
